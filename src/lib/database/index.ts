import { <PERSON>ysely, PostgresDialect } from 'kysely';
import { Pool } from 'pg';
import type { DB } from './types';

// Check if we're in development mode (fallback for non-SvelteKit contexts)
const isDev = process.env.NODE_ENV !== 'production';

// Database factory function that accepts configuration
export function createDatabase(config: {
	host: string;
	user: string;
	password: string;
	database: string;
	port: number;
}) {
	// Create connection pool
	const pool = new Pool({
		...config,
		max: isDev ? 10 : 20,
		idleTimeoutMillis: 30000,
		connectionTimeoutMillis: 10000
	});

	// Create database instance
	const database = new Kysely<DB>({
		dialect: new PostgresDialect({ pool }),
		log: (event) => {
			if (isDev && event.level === 'query') {
				console.log('SQL:', event.query.sql);
				console.log('Parameters:', event.query.parameters);
			}
		}
	});

	// Graceful shutdown
	process.on('SIGTERM', async () => {
		console.log('Closing database pool...');
		await pool.end();
	});

	return database;
}

// Export the database type consistently
export type Database = Kysely<DB>;

// Re-export types for convenience
export type { DB, MigrationsTable } from './types';
