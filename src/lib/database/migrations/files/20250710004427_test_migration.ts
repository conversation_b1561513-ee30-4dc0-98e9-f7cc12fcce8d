import { Kysely, sql } from 'kysely';

export async function up(db: <PERSON>ysely<any>): Promise<void> {
	await db.schema
		.createTable('test_table')
		.addColumn('id', 'uuid', (col) => col.primaryKey().defaultTo(sql`gen_random_uuid()`))
		.addColumn('name', 'varchar(255)', (col) => col.notNull())
		.addColumn('created_at', 'timestamptz', (col) => col.notNull().defaultTo(sql`CURRENT_TIMESTAMP`))
		.execute();
}

export async function down(db: Kysely<any>): Promise<void> {
	// Add rollback logic here (reverse of up function)
	// Example:
	/*
	await db.schema.dropTable('example_table').execute();
	*/
}
